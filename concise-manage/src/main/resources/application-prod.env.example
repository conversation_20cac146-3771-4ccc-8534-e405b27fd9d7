# 数据库配置环境变量示例
# 请复制此文件为 .env 或设置系统环境变量

# 主数据库配置
DB_MASTER_URL=************************************************************************************************************************************************************************************
DB_MASTER_USERNAME=your_db_username
DB_MASTER_PASSWORD=your_secure_password

# 数据中心数据库配置
DB_DATACENTER_URL=*******************************************************************************************************************************************************************************
DB_DATACENTER_USERNAME=your_db_username
DB_DATACENTER_PASSWORD=your_secure_password

# 教育分析数据库配置
DB_JYFX_URL=****************************************************************************************************************************************************************************
DB_JYFX_USERNAME=your_db_username
DB_JYFX_PASSWORD=your_secure_password

# 回流数据库配置
DB_HUILIU_URL=*************************************************************************************************************************************************************************************************
DB_HUILIU_USERNAME=your_db_username
DB_HUILIU_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_DATABASE=5
REDIS_PASSWORD=your_redis_password

# 本地环境单数据库配置（application-local.yml使用）
DB_URL=*****************************************************************************************************************************************************************************************************
DB_USERNAME=your_db_username
DB_PASSWORD=your_secure_password

# 浙政钉配置
ZZD_APPKEY=your_zzd_appkey
ZZD_APPSECRET=your_zzd_appsecret
ZZD_REMARK=your_app_remark
ZZD_DOMAIN=openplatform-pro.ding.zj.gov.cn
ZZD_TENANTID=your_tenant_id

# 认证中心配置
AUTH_CLIENT_ID=your_client_id
AUTH_CLIENT_SECRET=your_client_secret
AUTH_AUTHORIZE_URL=https://your-auth-server/auth/oauth2/authorize
AUTH_CODE_URL=https://your-auth-server/auth/oauth2/token
AUTH_TOKEN_URL=https://your-token-server/typt/public/user/getUserInfoByToken
AUTH_REVOKE_URL=https://your-auth-server/auth/oauth2/revoke
AUTH_LOGOUT_URL=https://your-auth-server/auth/logout
AUTH_REDIRECT_URI=https://your-app-domain/transfer
AUTH_SCOPE=AUTH
