# Mysql数据库 - 使用环境变量避免敏感信息泄露
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}


# 认证中心配置 - 使用环境变量避免敏感信息泄露
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: ${AUTH_CLIENT_ID}
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: ${AUTH_CLIENT_SECRET}
    # 认证中心授权地址（必填）
    authorize-url: ${AUTH_AUTHORIZE_URL}
    # 获取token的地址（必填）
    token-url: ${AUTH_TOKEN_URL}
    # 撤销token的地址（必填）
    revoke-url: ${AUTH_REVOKE_URL}
    # 登出地址（必填）
    logout-url: ${AUTH_LOGOUT_URL}
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: ${AUTH_REDIRECT_URI}
    # 授权范围（可选）- 默认为AUTH
    scope: ${AUTH_SCOPE}
