# 开发环境配置示例
# 请复制此文件为 .env 或设置系统环境变量

# 主数据库配置（开发环境）
DB_MASTER_URL=**************************************************************************************************************************************************************************************************************
DB_MASTER_USERNAME=dev_user
DB_MASTER_PASSWORD=dev_password

# 数据中心数据库配置（开发环境）
DB_DATACENTER_URL=*********************************************************************************************************************************************************************************************************
DB_DATACENTER_USERNAME=dev_user
DB_DATACENTER_PASSWORD=dev_password

# 教育分析数据库配置（开发环境）
DB_JYFX_URL=******************************************************************************************************************************************************************************************************
DB_JYFX_USERNAME=dev_user
DB_JYFX_PASSWORD=dev_password

# 回流数据库配置（开发环境）
DB_HUILIU_URL=*********************************************************************************************************************************************************************************************************
DB_HUILIU_USERNAME=dev_user
DB_HUILIU_PASSWORD=dev_password

# Redis配置（开发环境）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 浙政钉配置（开发环境）
ZZD_APPKEY=dev_appkey
ZZD_APPSECRET=dev_appsecret
ZZD_REMARK=dev_remark
ZZD_DOMAIN=openplatform-test.ding.zj.gov.cn
ZZD_TENANTID=dev_tenant_id

# 认证中心配置（开发环境）
AUTH_CLIENT_ID=dev_client_id
AUTH_CLIENT_SECRET=dev_client_secret
AUTH_AUTHORIZE_URL=https://dev-auth-server/auth
AUTH_TOKEN_URL=https://localhost:7100/typt/public/user/getUserInfoByToken
AUTH_REVOKE_URL=https://dev-auth-server/auth/oauth2/revoke
AUTH_LOGOUT_URL=https://dev-auth-server/auth/logout
AUTH_REDIRECT_URI=http://localhost:8050/#/transfer
AUTH_SCOPE=AUTH
