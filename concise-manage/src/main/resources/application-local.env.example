# 本地环境配置示例
# 请复制此文件为 .env 或设置系统环境变量

# 数据库配置（本地环境）
DB_URL=******************************************************************************************************************************************************************************************************
DB_USERNAME=local_user
DB_PASSWORD=local_password

# Redis配置（本地环境）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 认证中心配置（本地环境）
AUTH_CLIENT_ID=local_client_id
AUTH_CLIENT_SECRET=local_client_secret
AUTH_AUTHORIZE_URL=https://local-auth-server/auth
AUTH_TOKEN_URL=https://localhost:7100/typt/public/user/getUserInfoByToken
AUTH_REVOKE_URL=https://local-auth-server/auth/oauth2/revoke
AUTH_LOGOUT_URL=https://local-auth-server/auth/logout
AUTH_REDIRECT_URI=http://localhost:8050/#/transfer
AUTH_SCOPE=AUTH
