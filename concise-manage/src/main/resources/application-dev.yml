# Mysql数据库 - 开发环境也应使用环境变量避免敏感信息泄露
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_MASTER_URL}
          username: ${DB_MASTER_USERNAME}
          password: ${DB_MASTER_PASSWORD}
        dataCenter:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_DATACENTER_URL}
          username: ${DB_DATACENTER_USERNAME}
          password: ${DB_DATACENTER_PASSWORD}
        jyfx:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_JYFX_URL}
          username: ${DB_JYFX_USERNAME}
          password: ${DB_JYFX_PASSWORD}
        huiliu:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_HUILIU_URL}
          username: ${DB_HUILIU_USERNAME}
          password: ${DB_HUILIU_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise: debug
# 浙政钉配置 - 开发环境也应使用环境变量避免敏感信息泄露
zzd:
  qrcode:
    appkey: ${ZZD_APPKEY}
    appsecret: ${ZZD_APPSECRET}
    remark: ${ZZD_REMARK}
    domain: ${ZZD_DOMAIN}
    tenantid: ${ZZD_TENANTID}
# 认证中心配置 - 开发环境也应使用环境变量避免敏感信息泄露
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: ${AUTH_CLIENT_ID}
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: ${AUTH_CLIENT_SECRET}
    # 认证中心授权地址（必填）
    authorize-url: ${AUTH_AUTHORIZE_URL}
    # 获取token的地址（必填）
    token-url: ${AUTH_TOKEN_URL}
    # 撤销token的地址（必填）
    revoke-url: ${AUTH_REVOKE_URL}
    # 登出地址（必填）
    logout-url: ${AUTH_LOGOUT_URL}
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: ${AUTH_REDIRECT_URI}
    # 授权范围（可选）- 默认为AUTH
    scope: ${AUTH_SCOPE}
